#!/bin/bash

echo "🧪 测试前端应用..."

# 颜色定义
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 检查前端服务是否运行
check_frontend() {
    local ports=(3010 3011 3012)
    local frontend_url=""
    
    for port in "${ports[@]}"; do
        if curl -s "http://localhost:$port" > /dev/null 2>&1; then
            frontend_url="http://localhost:$port"
            break
        fi
    done
    
    if [ -n "$frontend_url" ]; then
        echo -e "${GREEN}✅ 前端服务运行正常: $frontend_url${NC}"
        return 0
    else
        echo -e "${RED}❌ 前端服务未运行${NC}"
        return 1
    fi
}

# 检查依赖是否安装
check_dependencies() {
    if [ ! -d "node_modules" ]; then
        echo -e "${YELLOW}⚠️  依赖未安装，正在安装...${NC}"
        npm install
        if [ $? -eq 0 ]; then
            echo -e "${GREEN}✅ 依赖安装成功${NC}"
        else
            echo -e "${RED}❌ 依赖安装失败${NC}"
            return 1
        fi
    else
        echo -e "${GREEN}✅ 依赖已安装${NC}"
    fi
    return 0
}

# 检查构建是否成功
test_build() {
    echo -e "${YELLOW}🔨 测试构建...${NC}"
    npm run build > /dev/null 2>&1
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✅ 构建成功${NC}"
        # 清理构建产物
        rm -rf dist
        return 0
    else
        echo -e "${RED}❌ 构建失败${NC}"
        return 1
    fi
}

# 主测试流程
main() {
    echo "开始测试前端应用..."
    echo ""
    
    # 检查依赖
    if ! check_dependencies; then
        exit 1
    fi
    
    # 测试构建
    if ! test_build; then
        exit 1
    fi
    
    # 检查服务
    if check_frontend; then
        echo ""
        echo -e "${GREEN}🎉 前端应用测试通过！${NC}"
        echo ""
        echo "📊 测试结果:"
        echo "  ✅ 依赖安装正常"
        echo "  ✅ 构建流程正常"
        echo "  ✅ 服务运行正常"
        echo ""
        echo "🌐 访问地址: $(curl -s http://localhost:3010 > /dev/null 2>&1 && echo "http://localhost:3010" || curl -s http://localhost:3011 > /dev/null 2>&1 && echo "http://localhost:3011" || echo "服务未运行")"
    else
        echo ""
        echo -e "${YELLOW}⚠️  前端服务未运行，请先启动:${NC}"
        echo "  npm run dev"
        echo ""
        echo "或使用启动脚本:"
        echo "  ./start.sh"
    fi
}

# 运行测试
main
