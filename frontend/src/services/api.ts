import axios from 'axios';
import { Schem<PERSON>, ParseResult, KnowledgeGraphStatus } from '../types';

const API_BASE_URL = '/api';

const api = axios.create({
  baseURL: API_BASE_URL,
  timeout: 300000,
});

// 请求拦截器
api.interceptors.request.use(
  (config) => {
    console.log('API Request:', config.method?.toUpperCase(), config.url);
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// 响应拦截器
api.interceptors.response.use(
  (response) => {
    console.log('API Response:', response.status, response.config.url);
    return response;
  },
  (error) => {
    console.error('API Error:', error.response?.status, error.config?.url, error.message);
    return Promise.reject(error);
  }
);

export const schemaApi = {
  // 获取所有模板
  getSchemas: async (): Promise<Schema[]> => {
    const response = await api.get('/schemas');
    return response.data;
  },

  // 获取单个模板
  getSchema: async (theme: string): Promise<Schema> => {
    const response = await api.get(`/schemas/${theme}`);
    return response.data;
  },

  // 创建模板
  createSchema: async (schema: Schema): Promise<void> => {
    await api.post('/schemas', schema);
  },

  // 更新模板
  updateSchema: async (theme: string, schema: Schema): Promise<void> => {
    await api.put(`/schemas/${theme}`, schema);
  },

  // 删除模板
  deleteSchema: async (theme: string): Promise<void> => {
    await api.delete(`/schemas/${theme}`);
  },
};

export const parseApi = {
  // 解析文件
  parseFile: async (theme: string, file: File): Promise<ParseResult> => {
    const formData = new FormData();
    formData.append('theme', theme);
    formData.append('file', file);

    const response = await api.post('/parse', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  },

  // 确认解析结果
  confirmParsedResult: async (theme: string, parsedResult: ParseResult): Promise<void> => {
    await api.post('/parse/confirm', {
      theme,
      parsed_result: parsedResult,
    });
  },

  // 获取支持的文件格式
  getSupportedFormats: async () => {
    const response = await api.get('/parse/supported_formats');
    return response.data;
  },
};

export const kgApi = {
  // 构建知识图谱
  buildKnowledgeGraph: async (theme: string, files: File[]): Promise<void> => {
    const formData = new FormData();
    formData.append('theme', theme);
    files.forEach((file) => {
      formData.append('files', file);
    });

    await api.post('/build_kg', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
  },

  // 获取知识图谱状态
  getKnowledgeGraphStatus: async (): Promise<KnowledgeGraphStatus> => {
    const response = await api.get('/kg_status');
    return response.data;
  },

  // 与知识图谱聊天
  chatWithKnowledgeGraph: async (question: string): Promise<ReadableStream> => {
    const response = await fetch(`${API_BASE_URL}/chat_kg`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ question }),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    return response.body!;
  },
};

export default api;
