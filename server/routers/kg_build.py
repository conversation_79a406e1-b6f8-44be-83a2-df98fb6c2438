from fastapi import APIRouter, UploadFile, File, Form, HTTPException
from fastapi.responses import StreamingResponse
from typing import List
from pydantic import BaseModel
import os
import shutil
import json
import time
from build.build_graph import KnowledgeGraphBuilder
from build.build_index_and_community import IndexCommunityBuilder
from build.build_chunk_index import ChunkIndexBuilder
from rich.console import Console
from rich.panel import Panel
from rich.text import Text
from agent.graph_agent import GraphAgent

router = APIRouter()

class ChatRequest(BaseModel):
    question: str

class KnowledgeGraphProcessor:
    """
    知识图谱处理器，整合了图谱构建和索引处理的完整流程。
    可以选择完整流程或单独执行其中一个步骤。
    """
    
    def __init__(self, theme: str = None):
        self.console = Console()
        self.theme = theme
        
    def process_all(self):
        """执行完整的知识图谱构建流程"""
        try:
            # 步骤1: 构建知识图谱
            self.console.print(Panel(Text("开始构建知识图谱...", style="bold blue"), border_style="blue"))
            kg_builder = KnowledgeGraphBuilder(theme=self.theme)
            kg_builder.process()

            # 步骤2: 构建索引和社区
            self.console.print(Panel(Text("开始构建索引和社区...", style="bold green"), border_style="green"))
            index_builder = IndexCommunityBuilder()
            index_builder.process()

            # 步骤3: 构建块索引
            self.console.print(Panel(Text("开始构建块索引...", style="bold yellow"), border_style="yellow"))
            chunk_builder = ChunkIndexBuilder()
            chunk_builder.process()
            
            success_text = Text("知识图谱构建完成！", style="bold green")
            self.console.print(Panel(success_text, border_style="green"))
            
        except Exception as e:
            error_text = Text(f"构建过程中出现错误: {str(e)}", style="bold red")
            self.console.print(Panel(error_text, border_style="red"))
            raise

async def test_agent_stream(agent, agent_name, query, thread_id, show_thinking=False, max_time=None):
    """测试特定Agent的流式响应"""
    if max_time is None:
        max_time = 300
        
    print(f"\n[测试] {agent_name} - 流式 - 查询: '{query}'")
    
    try:
        if not hasattr(agent, 'ask_stream'):
            print(f"[错误] {agent_name} 不支持流式输出")
            error_msg = "抱歉，当前系统不支持流式输出功能。"
            sse_data = f"data: {json.dumps({'content': error_msg}, ensure_ascii=False)}\n\n"
            yield sse_data
            return
        
        # 执行流式查询
        async for chunk in agent.ask_stream(query, thread_id=thread_id, **({"show_thinking": show_thinking} if agent_name.startswith("DeepResearchAgent") else {})):
            # 将字典类型的chunk转换为字符串
            if isinstance(chunk, dict):
                if 'answer' in chunk:
                    chunk_text = chunk['answer']
                    print("\n[接收到最终答案字典]")
                else:
                    chunk_text = str(chunk)
                    print("\n[接收到中间结果字典]")
            else:
                chunk_text = str(chunk)
            
            # 格式化为SSE格式
            if chunk_text and chunk_text.strip():
                sse_data = f"data: {json.dumps({'content': chunk_text}, ensure_ascii=False)}\n\n"
                yield sse_data
    
    except Exception as e:
        print(f"[错误] {agent_name} 流式处理查询时出错: {str(e)}")
        error_msg = f"抱歉，处理您的问题时出现了错误：{str(e)}"
        sse_data = f"data: {json.dumps({'content': error_msg}, ensure_ascii=False)}\n\n"
        yield sse_data

@router.post("/build_kg")
async def build_knowledge_graph(theme: str = Form(...), files: List[UploadFile] = File(...)):
    """构建知识图谱"""
    try:
        # 导入数据库管理器
        from config.database import db_manager

        # 检查schema是否存在
        schema = db_manager.load_schema_from_db(theme)
        if not schema:
            raise HTTPException(
                status_code=400,
                detail=f"主题 '{theme}' 的模板不存在。请先创建模板再构建知识图谱。"
            )

        # 确保files目录存在 (使用绝对路径)
        import os
        from pathlib import Path

        # 获取项目根目录
        current_dir = Path(__file__).resolve().parent.parent.parent
        files_dir = current_dir / "files"

        if not files_dir.exists():
            files_dir.mkdir(parents=True, exist_ok=True)

        # 保存上传的文件
        saved_files = []
        for file in files:
            if not file.filename:
                continue

            file_path = files_dir / file.filename
            with open(file_path, "wb") as buffer:
                shutil.copyfileobj(file.file, buffer)
            saved_files.append(file_path)

        if not saved_files:
            raise HTTPException(status_code=400, detail="没有有效的文件被上传")

        # 创建知识图谱处理器并执行构建
        processor = KnowledgeGraphProcessor(theme=theme)
        processor.process_all()

        return {
            "status": "success",
            "message": "知识图谱构建完成",
            "theme": theme,
            "files_processed": len(saved_files)
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"知识图谱构建失败: {str(e)}")

# @router.post("/chat_kg")
# async def chat_with_knowledge_graph(request: ChatRequest):
#     """基于知识图谱的对话"""
#     try:
#         # 检查是否有知识图谱数据
#         required_paths = ["./output", "./cache"]
#         has_data = any(os.path.exists(path) and os.listdir(path) for path in required_paths if os.path.exists(path))
        
#         if not has_data:
#             # 如果没有数据，返回提示信息
#             async def no_data_response():
#                 message = "抱歉，系统中还没有构建知识图谱数据。请先使用模板创建知识图谱，然后再进行对话。\n\n您可以：\n1. 在模板列表中选择一个模板\n2. 点击绿色的网络图标\n3. 上传相关文档\n4. 构建知识图谱\n5. 然后回到这里进行对话"
#                 sse_data = f"data: {json.dumps({'content': message}, ensure_ascii=False)}\n\n"
#                 yield sse_data
            
#             return StreamingResponse(
#                 no_data_response(),
#                 media_type="text/event-stream",
#                 headers={
#                     "Cache-Control": "no-cache",
#                     "Connection": "keep-alive",
#                     "Transfer-Encoding": "chunked"
#                 }
#             )
#     except Exception as e:
#         print(f"检查知识图谱数据时出错: {e}")
    
#     # 创建GraphAgent并处理对话
#     agent_name = "GraphAgent"
#     agent = GraphAgent()

#     # 为每个测试创建唯一的线程ID
#     thread_id = f"stream_{agent_name}_{int(time.time())}"
    
#     return StreamingResponse(
#         test_agent_stream(agent, agent_name, request.question, thread_id),
#         media_type="text/event-stream",
#         headers={
#             "Cache-Control": "no-cache",
#             "Connection": "keep-alive",
#             "Transfer-Encoding": "chunked"
#         }
#     )

@router.get("/kg_status")
async def get_knowledge_graph_status():
    """获取知识图谱状态"""
    try:
        status = {
            "has_output": os.path.exists("./output") and bool(os.listdir("./output")) if os.path.exists("./output") else False,
            "has_cache": os.path.exists("./cache") and bool(os.listdir("./cache")) if os.path.exists("./cache") else False,
            "files_count": len(os.listdir("./files")) if os.path.exists("./files") else 0
        }
        
        status["is_ready"] = status["has_cache"] or status["has_output"]
        
        return status
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取知识图谱状态失败: {str(e)}")
