#!/bin/bash

echo "🚀 启动 GraphRAG 完整系统..."

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 检查端口是否被占用
check_port() {
    local port=$1
    local service=$2
    if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null ; then
        echo -e "${YELLOW}警告: 端口 $port 已被占用 ($service)${NC}"
        return 1
    fi
    return 0
}

# 启动后端服务
start_backend() {
    echo -e "${BLUE}📡 启动后端服务 (端口: 8012)...${NC}"
    
    if ! check_port 8012 "后端服务"; then
        echo -e "${RED}后端端口被占用，请先停止相关服务${NC}"
        return 1
    fi
    
    # 检查Python环境
    if ! command -v python3 &> /dev/null; then
        echo -e "${RED}错误: 未找到 Python3${NC}"
        return 1
    fi
    
    # 安装后端依赖
    if [ ! -f "requirements.txt" ]; then
        echo -e "${RED}错误: 未找到 requirements.txt${NC}"
        return 1
    fi
    
    echo "安装后端依赖..."
    pip install -r requirements.txt > /dev/null 2>&1
    
    # 启动后端
    cd server
    python main.py &
    BACKEND_PID=$!
    cd ..
    
    echo -e "${GREEN}✅ 后端服务已启动 (PID: $BACKEND_PID)${NC}"
    return 0
}

# 启动前端服务
start_frontend() {
    echo -e "${BLUE}🌐 启动前端服务 (端口: 3010)...${NC}"
    
    if ! check_port 3010 "前端服务"; then
        echo -e "${RED}前端端口被占用，请先停止相关服务${NC}"
        return 1
    fi
    
    # 检查Node.js环境
    if ! command -v node &> /dev/null; then
        echo -e "${RED}错误: 未找到 Node.js${NC}"
        return 1
    fi
    
    if ! command -v npm &> /dev/null; then
        echo -e "${RED}错误: 未找到 npm${NC}"
        return 1
    fi
    
    # 进入前端目录
    cd frontend
    
    # 安装前端依赖
    if [ ! -d "node_modules" ]; then
        echo "安装前端依赖..."
        npm install > /dev/null 2>&1
    fi
    
    # 启动前端
    npm run dev &
    FRONTEND_PID=$!
    cd ..
    
    echo -e "${GREEN}✅ 前端服务已启动 (PID: $FRONTEND_PID)${NC}"
    return 0
}

# 等待服务启动
wait_for_services() {
    echo -e "${YELLOW}⏳ 等待服务启动...${NC}"
    sleep 5
    
    # 检查后端
    if curl -s http://localhost:8012 > /dev/null 2>&1; then
        echo -e "${GREEN}✅ 后端服务运行正常${NC}"
    else
        echo -e "${RED}❌ 后端服务启动失败${NC}"
    fi
    
    # 检查前端
    if curl -s http://localhost:3010 > /dev/null 2>&1; then
        echo -e "${GREEN}✅ 前端服务运行正常${NC}"
    else
        echo -e "${RED}❌ 前端服务启动失败${NC}"
    fi
}

# 显示服务信息
show_info() {
    echo ""
    echo -e "${GREEN}🎉 GraphRAG 系统启动完成！${NC}"
    echo ""
    echo -e "${BLUE}📊 服务信息:${NC}"
    echo -e "  • 前端地址: ${GREEN}http://localhost:3010${NC}"
    echo -e "  • 后端地址: ${GREEN}http://localhost:8012${NC}"
    echo ""
    echo -e "${YELLOW}💡 使用说明:${NC}"
    echo "  1. 打开浏览器访问 http://localhost:3010"
    echo "  2. 在模板管理页面创建知识图谱模板"
    echo "  3. 上传文档构建知识图谱"
    echo "  4. 在智能对话页面开始问答"
    echo ""
    echo -e "${BLUE}🛑 停止服务:${NC}"
    echo "  按 Ctrl+C 停止所有服务"
}

# 清理函数
cleanup() {
    echo ""
    echo -e "${YELLOW}🛑 正在停止服务...${NC}"
    
    if [ ! -z "$BACKEND_PID" ]; then
        kill $BACKEND_PID 2>/dev/null
        echo -e "${GREEN}✅ 后端服务已停止${NC}"
    fi
    
    if [ ! -z "$FRONTEND_PID" ]; then
        kill $FRONTEND_PID 2>/dev/null
        echo -e "${GREEN}✅ 前端服务已停止${NC}"
    fi
    
    echo -e "${GREEN}👋 再见！${NC}"
    exit 0
}

# 设置信号处理
trap cleanup SIGINT SIGTERM

# 主流程
main() {
    # 启动后端
    if ! start_backend; then
        echo -e "${RED}❌ 后端启动失败${NC}"
        exit 1
    fi
    
    # 启动前端
    if ! start_frontend; then
        echo -e "${RED}❌ 前端启动失败${NC}"
        cleanup
        exit 1
    fi
    
    # 等待服务启动
    wait_for_services
    
    # 显示信息
    show_info
    
    # 保持脚本运行
    while true; do
        sleep 1
    done
}

# 运行主函数
main
