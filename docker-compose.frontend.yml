version: '3.8'

services:
  # 前端服务
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    ports:
      - "3010:3010"
    environment:
      - NODE_ENV=production
    depends_on:
      - backend
    networks:
      - graphrag-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3010/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # 后端服务
  backend:
    build:
      context: .
      dockerfile: Dockerfile.backend
    ports:
      - "8012:8012"
    environment:
      - PYTHONPATH=/app
    volumes:
      - ./files:/app/files
      - ./cache:/app/cache
      - ./output:/app/output
    networks:
      - graphrag-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8012/health"]
      interval: 30s
      timeout: 10s
      retries: 3

networks:
  graphrag-network:
    driver: bridge

volumes:
  graphrag-data:
    driver: local
